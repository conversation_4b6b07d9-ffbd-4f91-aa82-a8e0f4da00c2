// This file is used to test our linting setup
// It contains intentional linting errors that should be caught

import React from 'react';
import { useState } from 'react';

// Unused import (should be caught by unused-imports plugin)
import { useEffect } from 'react';

// Missing return type (should be caught by explicit-function-return-type)
function TestComponent() {
	// Unused variable (should be caught by no-unused-vars)
	const unusedVar = 'test';

	// Using var instead of const/let (should be caught by no-var)
	var oldStyleVar = 'old';

	// Using == instead of === (should be caught by eqeqeq)
	if (oldStyleVar == 'old') {
		console.log('This should use ===');
	}

	// Missing curly braces (should be caught by curly)
	if (true) console.log('Missing braces');

	return React.createElement('div', null, 'Test');
}

export default TestComponent;
