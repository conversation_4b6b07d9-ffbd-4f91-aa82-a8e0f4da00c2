{"name": "kma-schedule-ngosangns", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "next lint --max-warnings 0", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest --testPathIgnorePatterns=src/__tests__/integration/", "test:unit": "jest --testPathIgnorePatterns=src/__tests__/integration/", "test:integration": "jest --config=jest.integration.config.js", "test:all": "npm run test:unit && npm run test:integration", "test:watch": "jest --watch --testPathIgnorePatterns=src/__tests__/integration/", "test:integration:watch": "jest --config=jest.integration.config.js --watch", "test:coverage": "jest --coverage --testPathIgnorePatterns=src/__tests__/integration/", "test:ci": "jest --ci --coverage --watchAll=false --testPathIgnorePatterns=src/__tests__/integration/", "prepare": "husky", "check-all": "npm run lint:strict && npm run format:check && npm run test:unit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "md5": "^2.3.0", "moment": "^2.29.4", "next": "14.0.4", "papaparse": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.59.0", "recharts": "^3.1.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/md5": "^2.3.2", "@types/node": "^20.10.5", "@types/papaparse": "^5.3.16", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.16", "dotenv": "^17.0.1", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2"}}